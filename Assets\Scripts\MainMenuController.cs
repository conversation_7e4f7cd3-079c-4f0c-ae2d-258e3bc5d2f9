using UnityEngine;
using UnityEngine.UIElements;
using UnityEngine.SceneManagement;

public class MainMenuController : MonoBehaviour
{
    private UIDocument menuDocument;
    private Button startGameBtn;
    private VisualElement menuContainer;

    private void OnEnable()
    {
        menuDocument = GetComponent<UIDocument>();
        var root = menuDocument.rootVisualElement;

        // Get button reference
        startGameBtn = root.Q<Button>("Startgame-btn");
        menuContainer = root.Q("menu-container");

        // Register button callback
        if (startGameBtn != null)
        {
            startGameBtn.clicked += OnStartGameClicked;
        }

        // Add fade-in effect
        if (menuContainer != null)
        {
            menuContainer.AddToClassList("fade-in");
        }
    }

    private void OnDisable()
    {
        if (startGameBtn != null)
        {
            startGameBtn.clicked -= OnStartGameClicked;
        }
    }

    private void OnStartGameClicked()
    {
        Debug.Log("PLAY NOW clicked! Starting the game...");

        // Here you can add your game start logic
        // For example, load the game scene:
        // SceneManager.LoadScene("GameScene");

        // Or show a loading screen, character selection, etc.
    }
}
