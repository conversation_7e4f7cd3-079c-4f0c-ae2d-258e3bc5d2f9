/* Unity USS Styles for Main Menu - Converted from your amazing CSS design! */

/* Root container with cosmic background */
.unity-ui-document__root {
    background-image: url('project://database/Assets/image/menu-background.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    width: 100%;
    height: 100%;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden;
}

/* Enhanced play button using PNG images */
.play-button {
    width: 300px;
    height: 100px;
    background-image: url('project://database/Assets/image/buttonstart.png');
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    background-color: transparent;
    border-width: 0;
    transition-duration: 0.3s;
    position: relative;
    justify-content: center;
    align-self: center;
    margin-top: 500px;
    color: transparent; /* Hide the text since we're using image */
}

/* Button hover effects */
.play-button:hover {
    background-image: url('project://database/Assets/image/buttonstarthover.png');
    translate: 0 -3px;
    scale: 1.05;
}

.play-button:active {
    background-image: url('project://database/Assets/image/buttonstartactive.png');
    translate: 0 1px;
    scale: 0.98;
}

/* Fade-in animation for menu container */
.fade-in {
    opacity: 1;
    transition-duration: 1s;
}


