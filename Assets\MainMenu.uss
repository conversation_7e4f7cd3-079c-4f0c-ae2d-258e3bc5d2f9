/* Unity USS Styles for Main Menu - Converted from your amazing CSS design! */

/* Root container with cosmic background */
.unity-ui-document__root {
    background-image: url('project://database/Assets/assets/image/menu-background.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    width: 100%;
    height: 100%;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden;
}

/* Enhanced play button matching the image design */
.play-button {
    width: 100%;
    padding: 10px;
    font-size: 32px;
    letter-spacing: 4px;
    background-color: transparent;
    color: rgb(255, 255, 255);
    -unity-text-align: middle-center;
    transition-duration: 0.3s;
    overflow: hidden;
    position: relative;
    justify-content: center;
    margin-top: 500px;
}

/* Button hover effects */
.play-button:hover {
    background-color: rgba(243, 206, 255, 0.43);
    border-color: rgba(224, 130, 236, 0.9);
    translate: 0 -3px;
    scale: 1.02;
}

.play-button:active {
    translate: 0 1px;
    scale: 0.98;
}

/* Fade-in animation for menu container */
.fade-in {
    opacity: 1;
    transition-duration: 1s;
}


